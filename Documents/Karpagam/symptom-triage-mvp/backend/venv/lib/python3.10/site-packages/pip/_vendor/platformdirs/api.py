from __future__ import annotations

import os
import sys
from abc import ABC, abstractmethod
from pathlib import Path

if sys.version_info >= (3, 8):  # pragma: no branch
    from typing import Literal  # pragma: no cover


class PlatformDirsABC(ABC):
    """
    Abstract base class for platform directories.
    """

    def __init__(
        self,
        appname: str | None = None,
        appauthor: str | None | Literal[False] = None,
        version: str | None = None,
        roaming: bool = False,
        multipath: bool = False,
        opinion: bool = True,
    ):
        """
        Create a new platform directory.

        :param appname: See `appname`.
        :param appauthor: See `appauthor`.
        :param version: See `version`.
        :param roaming: See `roaming`.
        :param multipath: See `multipath`.
        :param opinion: See `opinion`.
        """
        self.appname = appname  #: The name of application.
        self.appauthor = appauthor
        """
        The name of the app author or distributing body for this application. Typically, it is the owning company name.
        Defaults to `appname`. You may pass ``False`` to disable it.
        """
        self.version = version
        """
        An optional version path element to append to the path. You might want to use this if you want multiple versions
        of your app to be able to run independently. If used, this would typically be ``<major>.<minor>``.
        """
        self.roaming = roaming
        """
        Whether to use the roaming appdata directory on Windows. That means that for users on a Windows network setup
        for roaming profiles, this user data will be synced on login (see
        `here <http://technet.microsoft.com/en-us/library/cc766489(WS.10).aspx>`_).
        """
        self.multipath = multipath
        """
        An optional parameter only applicable to Unix/Linux which indicates that the entire list of data dirs should be
        returned. By default, the first item would only be returned.
        """
        self.opinion = opinion  #: A flag to indicating to use opinionated values.

    def _append_app_name_and_version(self, *base: str) -> str:
        params = list(base[1:])
        if self.appname:
            params.append(self.appname)
            if self.version:
                params.append(self.version)
        return os.path.join(base[0], *params)

    @property
    @abstractmethod
    def user_data_dir(self) -> str:
        """:return: data directory tied to the user"""

    @property
    @abstractmethod
    def site_data_dir(self) -> str:
        """:return: data directory shared by users"""

    @property
    @abstractmethod
    def user_config_dir(self) -> str:
        """:return: config directory tied to the user"""

    @property
    @abstractmethod
    def site_config_dir(self) -> str:
        """:return: config directory shared by the users"""

    @property
    @abstractmethod
    def user_cache_dir(self) -> str:
        """:return: cache directory tied to the user"""

    @property
    @abstractmethod
    def user_state_dir(self) -> str:
        """:return: state directory tied to the user"""

    @property
    @abstractmethod
    def user_log_dir(self) -> str:
        """:return: log directory tied to the user"""

    @property
    @abstractmethod
    def user_documents_dir(self) -> str:
        """:return: documents directory tied to the user"""

    @property
    @abstractmethod
    def user_runtime_dir(self) -> str:
        """:return: runtime directory tied to the user"""

    @property
    def user_data_path(self) -> Path:
        """:return: data path tied to the user"""
        return Path(self.user_data_dir)

    @property
    def site_data_path(self) -> Path:
        """:return: data path shared by users"""
        return Path(self.site_data_dir)

    @property
    def user_config_path(self) -> Path:
        """:return: config path tied to the user"""
        return Path(self.user_config_dir)

    @property
    def site_config_path(self) -> Path:
        """:return: config path shared by the users"""
        return Path(self.site_config_dir)

    @property
    def user_cache_path(self) -> Path:
        """:return: cache path tied to the user"""
        return Path(self.user_cache_dir)

    @property
    def user_state_path(self) -> Path:
        """:return: state path tied to the user"""
        return Path(self.user_state_dir)

    @property
    def user_log_path(self) -> Path:
        """:return: log path tied to the user"""
        return Path(self.user_log_dir)

    @property
    def user_documents_path(self) -> Path:
        """:return: documents path tied to the user"""
        return Path(self.user_documents_dir)

    @property
    def user_runtime_path(self) -> Path:
        """:return: runtime path tied to the user"""
        return Path(self.user_runtime_dir)
